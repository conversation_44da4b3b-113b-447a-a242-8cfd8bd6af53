'use client'

import React, { useRef, useState } from 'react'
import { ChevronLeft, ChevronRight, ArrowRight } from 'lucide-react'

interface CarouselItem {
  id: string
  title: string
  description: string
  beforeImage: string
  afterImage: string
  href: string
  alt: string
}

interface ToolCarouselProps {
  items: CarouselItem[]
  className?: string
}

const ToolCarousel: React.FC<ToolCarouselProps> = ({
  items,
  className = '',
}) => {
  const scrollContainerRef = useRef<HTMLDivElement>(null)
  const [canScrollLeft, setCanScrollLeft] = useState(false)
  const [canScrollRight, setCanScrollRight] = useState(true)

  const checkScrollButtons = () => {
    if (scrollContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } =
        scrollContainerRef.current
      setCanScrollLeft(scrollLeft > 0)
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1)
    }
  }

  const scroll = (direction: 'left' | 'right') => {
    if (scrollContainerRef.current) {
      const container = scrollContainerRef.current
      const cardWidth = window.innerWidth >= 1024 ? 360 : 280 // lg:w-[360px] : w-[280px]
      const gap = 32 // gap-8 = 2rem = 32px
      const scrollAmount = cardWidth + gap

      const newScrollLeft =
        container.scrollLeft +
        (direction === 'left' ? -scrollAmount : scrollAmount)

      container.scrollTo({
        left: newScrollLeft,
        behavior: 'smooth',
      })
    }
  }

  // Keyboard navigation
  React.useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'ArrowLeft') {
        event.preventDefault()
        scroll('left')
      } else if (event.key === 'ArrowRight') {
        event.preventDefault()
        scroll('right')
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [])

  React.useEffect(() => {
    checkScrollButtons()
    const container = scrollContainerRef.current
    if (container) {
      container.addEventListener('scroll', checkScrollButtons)
      // Check scroll buttons on resize
      const handleResize = () => {
        setTimeout(checkScrollButtons, 100)
      }
      window.addEventListener('resize', handleResize)

      return () => {
        container.removeEventListener('scroll', checkScrollButtons)
        window.removeEventListener('resize', handleResize)
      }
    }
  }, [])

  return (
    <div className={`relative w-full ${className}`}>
      {/* Navigation Buttons */}
      <button
        onClick={() => scroll('left')}
        disabled={!canScrollLeft}
        className={`absolute left-4 top-1/2 -translate-y-1/2 z-10 w-10 h-10 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 flex items-center justify-center transition-all duration-300 ${
          canScrollLeft
            ? 'opacity-100 hover:bg-white/20 hover:scale-110'
            : 'opacity-50 cursor-not-allowed'
        }`}
        aria-label="Previous items"
      >
        <ChevronLeft className="w-5 h-5 text-white" />
      </button>

      <button
        onClick={() => scroll('right')}
        disabled={!canScrollRight}
        className={`absolute right-4 top-1/2 -translate-y-1/2 z-10 w-10 h-10 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 flex items-center justify-center transition-all duration-300 ${
          canScrollRight
            ? 'opacity-100 hover:bg-white/20 hover:scale-110'
            : 'opacity-50 cursor-not-allowed'
        }`}
        aria-label="Next items"
      >
        <ChevronRight className="w-5 h-5 text-white" />
      </button>

      {/* Carousel Container */}
      <div
        ref={scrollContainerRef}
        className="flex gap-8 overflow-x-auto px-[max(20px,calc((100%-1496px)/2))] pb-4"
        style={{
          scrollbarWidth: 'none',
          msOverflowStyle: 'none',
          WebkitOverflowScrolling: 'touch',
        }}
        onScroll={checkScrollButtons}
      >
        {items.map((item) => (
          <div key={item.id} className="shrink-0">
            <a
              href={item.href}
              className="group relative flex h-[450px] w-[280px] flex-col justify-between overflow-hidden rounded-lg lg:h-[570px] lg:w-[360px] p-6 md:p-8 bg-slate-800/50 backdrop-blur-sm border border-slate-700/50 transition-all duration-300 hover:scale-[1.02] hover:border-cyan-500/50 hover:shadow-2xl hover:shadow-cyan-500/20"
            >
              {/* Before Image (default) */}
              <img
                alt={item.alt}
                loading="lazy"
                decoding="async"
                className="absolute inset-0 object-cover transition-all duration-700 group-hover:opacity-0 group-hover:scale-110"
                style={{
                  position: 'absolute',
                  height: '100%',
                  width: '100%',
                  left: 0,
                  top: 0,
                  right: 0,
                  bottom: 0,
                  color: 'transparent',
                }}
                src={item.beforeImage}
              />

              {/* After Image (on hover) */}
              <img
                alt={`${item.alt} - After`}
                loading="lazy"
                decoding="async"
                className="absolute inset-0 object-cover transition-all duration-700 opacity-0 scale-110 group-hover:opacity-100 group-hover:scale-100"
                style={{
                  position: 'absolute',
                  height: '100%',
                  width: '100%',
                  left: 0,
                  top: 0,
                  right: 0,
                  bottom: 0,
                  color: 'transparent',
                }}
                src={item.afterImage}
              />

              {/* Gradient Overlays */}
              <div
                aria-hidden="true"
                className="absolute inset-0 bg-gradient-to-b from-black/50 via-transparent to-black/80"
              />
              <div
                aria-hidden="true"
                className="absolute inset-0 bg-gradient-to-br from-cyan-500/10 to-purple-500/10 opacity-0 transition-opacity duration-500 ease-in-out group-hover:opacity-100"
              />

              {/* Content */}
              <h4 className="relative m-0 font-sans text-xl sm:text-2xl md:text-3xl font-bold leading-tight text-white group-hover:text-cyan-400 transition-colors duration-300">
                {item.title}
              </h4>

              <div className="relative flex flex-col justify-between gap-4 md:gap-5">
                <p className="line-clamp-3 text-base md:text-lg lg:text-xl text-gray-200 group-hover:text-white transition-colors duration-300">
                  {item.description}
                </p>
                <div className="flex items-center justify-end text-white group-hover:text-cyan-400 transition-colors duration-300">
                  <ArrowRight className="w-4 h-4 transition-transform duration-300 group-hover:translate-x-1" />
                  <span className="sr-only">
                    {item.title} - {item.description}
                  </span>
                </div>
              </div>
            </a>
          </div>
        ))}
      </div>

      {/* Custom scrollbar styles */}
      <style jsx>{`
        div::-webkit-scrollbar {
          display: none;
        }
      `}</style>
    </div>
  )
}

export default ToolCarousel
