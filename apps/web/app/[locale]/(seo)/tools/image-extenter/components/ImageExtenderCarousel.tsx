'use client'

import React from 'react'
import ToolCarousel from './ToolCarousel'
import { useTranslations } from 'next-intl'

const ImageExtenderCarousel: React.FC = () => {
  const t = useTranslations('imageExtenter')

  const carouselItems = [
    {
      id: 'portrait-to-landscape',
      title: t('carouselItem1Title') || 'Portrait to Landscape',
      description: t('carouselItem1Description') || 'Transform vertical photos into stunning landscape images',
      beforeImage: '/images/image-extenter/before-portrait.jpg',
      afterImage: '/images/image-extenter/after-landscape.jpg',
      href: '/tools/image-extenter',
      alt: 'Portrait photo transformed to landscape using AI image extender'
    },
    {
      id: 'mobile-wallpaper',
      title: t('carouselItem2Title') || 'Mobile Wallpaper',
      description: t('carouselItem2Description') || 'Create perfect mobile wallpapers from any image',
      beforeImage: '/images/image-extenter/before-square.jpg',
      afterImage: '/images/image-extenter/after-mobile.jpg',
      href: '/tools/image-extender',
      alt: 'Square image extended to mobile wallpaper format'
    },
    {
      id: 'social-media',
      title: t('carouselItem3Title') || 'Social Media Ready',
      description: t('carouselItem3Description') || 'Extend images to fit any social media platform',
      beforeImage: '/images/image-extenter/before-crop.jpg',
      afterImage: '/images/image-extenter/after-social.jpg',
      href: '/tools/image-extender',
      alt: 'Cropped image extended for social media use'
    },
    {
      id: 'background-extension',
      title: t('carouselItem4Title') || 'Background Extension',
      description: t('carouselItem4Description') || 'Seamlessly extend backgrounds with AI',
      beforeImage: '/images/image-extenter/before-tight.jpg',
      afterImage: '/images/image-extenter/after-extended.jpg',
      href: '/tools/image-extender',
      alt: 'Tight crop extended with AI-generated background'
    },
    {
      id: 'product-showcase',
      title: t('carouselItem5Title') || 'Product Showcase',
      description: t('carouselItem5Description') || 'Create professional product presentations',
      beforeImage: '/images/image-extenter/before-product.jpg',
      afterImage: '/images/image-extenter/after-showcase.jpg',
      href: '/tools/image-extender',
      alt: 'Product photo enhanced with extended background'
    },
    {
      id: 'artistic-expansion',
      title: t('carouselItem6Title') || 'Artistic Expansion',
      description: t('carouselItem6Description') || 'Expand artwork while maintaining style',
      beforeImage: '/images/image-extenter/before-art.jpg',
      afterImage: '/images/image-extenter/after-artistic.jpg',
      href: '/tools/image-extender',
      alt: 'Artwork expanded with AI while maintaining artistic style'
    }
  ]

  return (
    <section className="py-12 md:py-20 bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 relative overflow-hidden">
      {/* Enhanced background decoration */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-1/4 w-32 h-32 md:w-64 md:h-64 bg-cyan-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute bottom-20 right-1/4 w-24 h-24 md:w-48 md:h-48 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-blue-500/5 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-7xl mx-auto">
          {/* Title section */}
          <div className="text-center mb-12 md:mb-16">
            <div className="inline-flex items-center gap-2 bg-cyan-500/10 rounded-full px-3 py-1.5 md:px-4 md:py-2 mb-4 md:mb-6 border border-cyan-500/20">
              <div className="w-2 h-2 bg-cyan-400 rounded-full animate-pulse"></div>
              <span className="text-cyan-300 text-xs md:text-sm font-medium">
                {t('carouselBadge') || 'AI Image Extension'}
              </span>
            </div>

            <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4 md:mb-6 px-4">
              {t('carouselTitle') || 'See the Magic in Action'}
            </h2>
            <p className="text-base md:text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed px-4">
              {t('carouselDescription') || 'Discover how our AI image extender transforms your photos. Hover over each example to see the before and after transformation.'}
            </p>
          </div>

          {/* Carousel */}
          <ToolCarousel items={carouselItems} className="mb-8" />

          {/* Call to action */}
          <div className="text-center">
            <a
              href="/tools/image-extender"
              className="inline-flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-cyan-500 to-purple-500 text-white font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 group"
            >
              <span>{t('carouselCTA') || 'Try Image Extender Now'}</span>
              <svg
                className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M13 7l5 5m0 0l-5 5m5-5H6"
                />
              </svg>
            </a>
          </div>
        </div>
      </div>
    </section>
  )
}

export default ImageExtenderCarousel
