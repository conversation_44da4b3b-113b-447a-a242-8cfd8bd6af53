'use client'

import React from 'react'
import ToolCarousel from './ToolCarousel'

const ToolCarouselExample: React.FC = () => {
  const carouselItems = [
    {
      id: 'ai-assistant',
      title: 'AI Assistant',
      description: 'Create and edit images with AI',
      beforeImage: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=600&h=800&fit=crop&q=80',
      afterImage: 'https://images.unsplash.com/photo-1686191128892-3b4e0e8b8e8e?w=600&h=800&fit=crop&q=80',
      href: '/ai/assistant',
      alt: 'AI Assistant tool for creating and editing images'
    },
    {
      id: 'video-generator',
      title: 'AI video generator',
      description: 'Create stunning videos from text or images',
      beforeImage: 'https://images.unsplash.com/photo-1574375927938-d5a98e8ffe85?w=600&h=800&fit=crop&q=80',
      afterImage: 'https://images.unsplash.com/photo-1611162617474-5b21e879e113?w=600&h=800&fit=crop&q=80',
      href: '/ai/video-generator',
      alt: 'AI video generator for creating videos from text or images'
    },
    {
      id: 'video-editor',
      title: 'Video Editor',
      description: 'Create video projects instantly',
      beforeImage: 'https://images.unsplash.com/photo-1536431311719-398b6704d4cc?w=600&h=800&fit=crop&q=80',
      afterImage: 'https://images.unsplash.com/photo-1574375927938-d5a98e8ffe85?w=600&h=800&fit=crop&q=80',
      href: '/ai/video-editor',
      alt: 'Video editor for creating video projects instantly'
    },
    {
      id: 'reimagine',
      title: 'Reimagine',
      description: 'Variations with AI',
      beforeImage: 'https://images.unsplash.com/photo-1547036967-23d11aacaee0?w=600&h=800&fit=crop&q=80',
      afterImage: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=600&h=800&fit=crop&q=80',
      href: '/ai/reimagine',
      alt: 'Reimagine tool for creating AI variations'
    },
    {
      id: 'upscale',
      title: 'Upscale',
      description: 'Increase resolution and details',
      beforeImage: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=600&h=800&fit=crop&q=80',
      afterImage: 'https://images.unsplash.com/photo-1547036967-23d11aacaee0?w=600&h=800&fit=crop&q=80',
      href: '/ai/upscale',
      alt: 'Upscale tool for increasing image resolution and details'
    },
    {
      id: 'retouch',
      title: 'Retouch',
      description: 'Replace details easily',
      beforeImage: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=600&h=800&fit=crop&q=80',
      afterImage: 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=600&h=800&fit=crop&q=80',
      href: '/ai/retouch',
      alt: 'Retouch tool for replacing image details easily'
    },
    {
      id: 'background-remover',
      title: 'Background Remover',
      description: 'Erase any image background',
      beforeImage: 'https://images.unsplash.com/photo-1611162617474-5b21e879e113?w=600&h=800&fit=crop&q=80',
      afterImage: 'https://images.unsplash.com/photo-1536431311719-398b6704d4cc?w=600&h=800&fit=crop&q=80',
      href: '/ai/background-remover',
      alt: 'Background remover tool for erasing image backgrounds'
    },
    {
      id: 'text-to-speech',
      title: 'Text to Speech',
      description: 'Pro-quality voiceovers in seconds',
      beforeImage: 'https://images.unsplash.com/photo-1686191128892-3b4e0e8b8e8e?w=600&h=800&fit=crop&q=80',
      afterImage: 'https://images.unsplash.com/photo-1677442136019-21780ecad995?w=600&h=800&fit=crop&q=80',
      href: '/ai/text-to-speech',
      alt: 'Text to speech tool for creating pro-quality voiceovers'
    }
  ]

  return (
    <section className="py-12 md:py-20 bg-slate-900 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-1/4 w-32 h-32 md:w-64 md:h-64 bg-blue-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-1/4 w-24 h-24 md:w-48 md:h-48 bg-purple-500/10 rounded-full blur-3xl"></div>
      </div>

      <div className="w-full mx-auto px-4 relative z-10">
        <div className="w-full mx-auto">
          {/* Title section */}
          <div className="text-center mb-12 md:mb-16">
            <h2 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4 md:mb-6 px-4">
              Explore Our AI Tools
            </h2>
            <p className="text-base md:text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed px-4">
              Discover powerful AI tools that transform your creative workflow. Hover over each card to see the transformation.
            </p>
          </div>

          {/* Carousel */}
          <ToolCarousel items={carouselItems} />
        </div>
      </div>
    </section>
  )
}

export default ToolCarouselExample
