'use client'

import React, { useState } from 'react'
import {
  ChevronDown,
  ChevronUp,
  HelpCircle,
  Shield,
  Zap,
  Brain,
  Settings,
} from 'lucide-react'
import { useTranslations } from 'next-intl'
import { Link } from '@i18n/routing'

interface FAQSectionProps {
  toolUrl: string
}

const FAQSection: React.FC<FAQSectionProps> = ({ toolUrl }) => {
  const t = useTranslations('imageExtenter')
  const [openFAQ, setOpenFAQ] = useState<number | null>(0)

  const faqs = [
    {
      id: 1,
      icon: <Zap className="w-5 h-5" />,
      question: t('faq1Question'),
      answer: t('faq1Answer'),
      gradient: 'from-cyan-500 to-blue-600',
    },
    {
      id: 2,
      icon: <Settings className="w-5 h-5" />,
      question: t('faq2Question'),
      answer: t('faq2Answer'),
      gradient: 'from-purple-500 to-pink-600',
    },
    {
      id: 3,
      icon: <Brain className="w-5 h-5" />,
      question: t('faq3Question'),
      answer: t('faq3Answer'),
      gradient: 'from-green-500 to-teal-600',
    },
    {
      id: 4,
      icon: <Shield className="w-5 h-5" />,
      question: t('faq4Question'),
      answer: t('faq4Answer'),
      gradient: 'from-orange-500 to-red-600',
    },
  ]

  const toggleFAQ = (id: number) => {
    setOpenFAQ(openFAQ === id ? null : id)
  }

  return (
    <section className="py-20 bg-slate-900 relative overflow-hidden">
      {/* 背景装饰 */}
      <div className="absolute inset-0">
        <div className="absolute top-10 left-1/4 w-48 h-48 bg-blue-500/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-10 right-1/4 w-32 h-32 bg-blue-500/10 rounded-full blur-3xl"></div>
      </div>

      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-4xl mx-auto">
          {/* 标题部分 */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 bg-blue-500/10 rounded-full px-4 py-2 mb-6 border border-blue-500/20">
              <HelpCircle className="w-4 h-4 text-blue-400" />
              <span className="text-blue-300 text-sm font-medium">
                {t('gotQuestionsBadge')}
              </span>
            </div>

            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              {t('faqTitle')}
            </h2>

            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              {t('faqDescription')}
            </p>
          </div>

          {/* FAQ列表 */}
          <div className="space-y-4">
            {faqs.map((faq, index) => (
              <div
                key={faq.id}
                className="group bg-gradient-to-br from-slate-800/50 to-slate-900/50 backdrop-blur-sm rounded-2xl border border-slate-700/50 hover:border-cyan-500/50 transition-all duration-300 overflow-hidden"
              >
                <button
                  onClick={() => toggleFAQ(faq.id)}
                  className="w-full p-6 md:p-8 text-left flex items-center justify-between gap-4 hover:bg-slate-800/30 transition-colors duration-300"
                >
                  <div className="flex items-center gap-4 flex-1">
                    {/* <div
                      className={`w-10 h-10 bg-gradient-to-br ${faq.gradient} rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}
                    >
                      {faq.icon}
                    </div> */}
                    <h3 className="text-lg md:text-xl font-semibold text-white group-hover:text-cyan-400 transition-colors duration-300">
                      {faq.question}
                    </h3>
                  </div>

                  <div className="flex-shrink-0">
                    {openFAQ === faq.id ? (
                      <ChevronUp className="w-6 h-6 text-cyan-400 transform transition-transform duration-300" />
                    ) : (
                      <ChevronDown className="w-6 h-6 text-gray-400 group-hover:text-cyan-400 transition-colors duration-300" />
                    )}
                  </div>
                </button>

                {/* 答案内容 */}
                <div
                  className={`overflow-hidden transition-all duration-500 ease-in-out ${
                    openFAQ === faq.id
                      ? 'max-h-96 opacity-100'
                      : 'max-h-0 opacity-0'
                  }`}
                >
                  <div className="px-6 md:px-8 pb-6 md:pb-8">
                    <div className="">
                      <div className="w-full h-px bg-gradient-to-r from-cyan-400/50 to-purple-400/50 mb-4"></div>
                      <p className="text-gray-300 text-lg leading-relaxed">
                        {faq.answer}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* 额外帮助信息 */}
          <div className="mt-16 text-center">
            <div className="bg-gradient-to-br from-slate-800/50 to-slate-900/50 backdrop-blur-sm rounded-2xl p-8 border border-slate-700/50">
              <div className="flex items-center justify-center gap-3 mb-4">
                <HelpCircle className="w-6 h-6 text-cyan-400" />
                <h3 className="text-2xl font-bold text-white">
                  {t('stillHaveQuestionsTitle')}
                </h3>
              </div>

              <p className="text-gray-300 text-lg mb-6">
                {t('stillHaveQuestionsDescription')}
              </p>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/contact">
                  <button className="px-6 py-3 bg-gradient-to-r from-cyan-500 to-purple-600 text-white font-semibold rounded-xl hover:shadow-lg hover:shadow-cyan-500/25 transition-all duration-300 hover:scale-105">
                    {t('contactSupportButton')}
                  </button>
                </Link>
              </div>
            </div>
          </div>

          {/* 快速统计 */}
          <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center p-6 bg-gradient-to-br from-slate-800/30 to-slate-900/30 backdrop-blur-sm rounded-xl border border-slate-700/30">
              <div className="text-2xl font-bold text-cyan-400 mb-2">24/7</div>
              <div className="text-gray-300 text-sm">
                {t('supportAvailableStat')}
              </div>
            </div>
            <div className="text-center p-6 bg-gradient-to-br from-slate-800/30 to-slate-900/30 backdrop-blur-sm rounded-xl border border-slate-700/30">
              <div className="text-2xl font-bold text-purple-400 mb-2">
                99.9%
              </div>
              <div className="text-gray-300 text-sm">
                {t('uptimeGuaranteeStat')}
              </div>
            </div>
            <div className="text-center p-6 bg-gradient-to-br from-slate-800/30 to-slate-900/30 backdrop-blur-sm rounded-xl border border-slate-700/30">
              <div className="text-2xl font-bold text-pink-400 mb-2">
                &lt;30s
              </div>
              <div className="text-gray-300 text-sm">
                {t('processingTimeStat')}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default FAQSection
