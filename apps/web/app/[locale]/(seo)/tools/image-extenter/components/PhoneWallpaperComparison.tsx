'use client'

import React from 'react'

interface PhoneWallpaperComparisonProps {
  originalImage: string
  extendedImage: string
  alt: string
  originalAspect?: string
  extendedAspect?: string
  customAcpect?: boolean
}

const PhoneWallpaperComparison: React.FC<PhoneWallpaperComparisonProps> = ({
  originalImage,
  extendedImage,
  alt,
  originalAspect = 'aspect-square',
  extendedAspect = 'aspect-[208/384]',
  customAcpect,
}) => {
  return (
    <div className="relative flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-6 md:gap-8 p-4 sm:p-6 md:p-8">
      {/* 左侧 - 原始正方形图片 */}
      <div className="relative group">
        <div className="relative overflow-hidden rounded-xl md:rounded-2xl bg-gradient-to-br from-slate-800/50 to-slate-900/50 backdrop-blur-sm transition-all duration-500">
          <div className={`w-32 sm:w-40 md:w-56 ${originalAspect} relative`}>
            <img
              src={originalImage}
              alt={`Original ${alt}`}
              className="w-full h-full object-cover transform transition duration-700 group-hover:scale-105"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-black/20"></div>

            {/* 悬浮效果 */}
            <div className="absolute inset-0 bg-gradient-to-br from-cyan-500/10 to-purple-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
          </div>
        </div>

        {/* 标签 */}
        <div className="absolute -bottom-3 left-1/2 transform -translate-x-1/2">
          <div className="bg-slate-800 border border-slate-600 rounded-full px-2 md:px-3 py-1 text-xs text-gray-300 font-medium">
            Original
          </div>
        </div>
      </div>

      {/* 箭头指示器 */}
      {/* <div className="flex flex-col items-center">
        <div className="w-8 h-0.5 bg-gradient-to-r from-cyan-400 to-purple-400 mb-2"></div>
        <svg
          className="w-6 h-6 text-cyan-400"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M9 5l7 7-7 7"
          />
        </svg>
        <div className="w-8 h-0.5 bg-gradient-to-r from-cyan-400 to-purple-400 mt-2"></div>
      </div> */}

      {/* 右侧 - 手机形状的扩展图片 */}
      <div className="relative group sm:ml-2 md:ml-4">
        <div className="relative">
          {/* 手机外框 */}
          <div
            className={`relative w-36 sm:w-48 md:w-64 ${extendedAspect} bg-gradient-to-b from-slate-700 to-slate-800 rounded-[1.5rem] sm:rounded-[2rem] md:rounded-[2.5rem] p-1.5 sm:p-2 shadow-2xl transition-all duration-500`}
          >
            {/* 屏幕区域 */}
            <div className="w-full h-full bg-black rounded-[1.25rem] sm:rounded-[1.5rem] md:rounded-[2rem] overflow-hidden relative">
              {/* 刘海/动态岛 */}
              {!customAcpect && (
                <div className="absolute top-2 sm:top-3 left-1/2 transform -translate-x-1/2 w-12 sm:w-16 md:w-20 h-4 sm:h-5 md:h-7 bg-slate-800 rounded-full z-10"></div>
              )}

              {/* 扩展后的图片 */}
              <img
                src={extendedImage}
                alt={`Extended ${alt}`}
                className="w-full h-full object-cover transform transition duration-700 group-hover:scale-105"
              />

              {!customAcpect && (
                <>
                  <div className="absolute inset-0 bg-gradient-to-br from-white/5 via-transparent to-transparent"></div>

                  <div className="absolute inset-0 bg-gradient-to-br from-cyan-500/10 to-purple-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                </>
              )}
            </div>

            {/* 手机按键 */}
            {!customAcpect && (
              <>
                <div className="absolute right-0 top-16 sm:top-20 md:top-24 w-0.5 sm:w-1 h-6 sm:h-8 md:h-10 bg-slate-600 rounded-l-sm"></div>
                <div className="absolute right-0 top-28 sm:top-32 md:top-40 w-0.5 sm:w-1 h-8 sm:h-10 md:h-14 bg-slate-600 rounded-l-sm"></div>
                <div className="absolute right-0 top-40 sm:top-48 md:top-60 w-0.5 sm:w-1 h-8 sm:h-10 md:h-14 bg-slate-600 rounded-l-sm"></div>
              </>
            )}
          </div>
        </div>

        {/* 标签 */}
        <div className="absolute -bottom-3 left-1/2 transform -translate-x-1/2">
          <div className="bg-gradient-to-r from-cyan-500 to-purple-500 rounded-full px-2 md:px-3 py-1 text-xs text-white font-medium">
            Extended
          </div>
        </div>
      </div>

      {/* 背景装饰 */}
      <div className="absolute inset-0 -z-10">
        <div className="absolute top-1/2 left-1/4 w-20 h-20 sm:w-32 sm:h-32 md:w-40 md:h-40 bg-cyan-500/5 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 right-1/4 w-20 h-20 sm:w-32 sm:h-32 md:w-40 md:h-40 bg-purple-500/5 rounded-full blur-3xl"></div>
      </div>
    </div>
  )
}

export default PhoneWallpaperComparison
