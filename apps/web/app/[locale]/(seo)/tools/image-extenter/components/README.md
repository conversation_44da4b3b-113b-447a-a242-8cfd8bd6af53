# Tool Carousel Components

这个文件夹包含了与示例图片样式相同的轮播组件，支持左右滑动和 hover 图片切换功能。

## 组件说明

### 1. ToolCarousel.tsx
主要的轮播组件，具有以下特性：

**功能特性：**
- ✅ 水平滚动轮播
- ✅ 左右导航按钮
- ✅ 键盘导航支持（左右箭头键）
- ✅ 响应式设计
- ✅ Before/After 图片 hover 切换
- ✅ 平滑滚动动画
- ✅ 自动隐藏滚动条
- ✅ 触摸滑动支持

**样式特性：**
- ✅ 与示例图片相同的卡片样式
- ✅ 渐变背景遮罩
- ✅ Hover 时的缩放和发光效果
- ✅ 图片缩放动画
- ✅ 颜色过渡动画

### 2. ImageExtenderCarousel.tsx
专门为图片扩展工具定制的轮播组件，包含：
- 预设的图片扩展示例数据
- 国际化支持
- 增强的背景装饰
- 行动号召按钮

### 3. ToolCarouselExample.tsx
通用轮播组件的使用示例

## 使用方法

### 基础使用

```tsx
import ToolCarousel from './ToolCarousel'

const items = [
  {
    id: 'example-1',
    title: 'AI Assistant',
    description: 'Create and edit images with AI',
    beforeImage: '/images/before.jpg',
    afterImage: '/images/after.jpg',
    href: '/ai/assistant',
    alt: 'AI Assistant tool'
  }
  // ... 更多项目
]

<ToolCarousel items={items} />
```

### 图片扩展工具专用版本

```tsx
import ImageExtenderCarousel from './ImageExtenderCarousel'

// 直接使用，包含预设数据和国际化
<ImageExtenderCarousel />
```

## 数据结构

```typescript
interface CarouselItem {
  id: string           // 唯一标识符
  title: string        // 卡片标题
  description: string  // 卡片描述
  beforeImage: string  // 默认显示的图片
  afterImage: string   // hover 时显示的图片
  href: string         // 点击跳转链接
  alt: string          // 图片 alt 文本
}
```

## 样式定制

组件使用 Tailwind CSS，可以通过以下方式定制：

1. **修改卡片尺寸：** 调整 `h-[450px] w-[280px] lg:h-[570px] lg:w-[360px]`
2. **修改间距：** 调整 `gap-8` 类
3. **修改颜色：** 调整渐变和 hover 颜色类
4. **修改动画：** 调整 `transition-*` 和 `duration-*` 类

## 键盘导航

- **左箭头键：** 向左滚动
- **右箭头键：** 向右滚动

## 浏览器兼容性

- ✅ Chrome 60+
- ✅ Firefox 60+
- ✅ Safari 12+
- ✅ Edge 79+

## 性能优化

- 图片懒加载
- 平滑滚动
- 防抖滚动检测
- 响应式图片尺寸计算

## 注意事项

1. 确保图片路径正确且图片已优化
2. 为每个项目提供有意义的 alt 文本
3. 测试不同屏幕尺寸下的显示效果
4. 确保 href 链接有效
