'use client'

import React, { useState, useEffect } from 'react'
import { ChevronLeft, ChevronRight, Download, Sparkles } from 'lucide-react'
import { Link } from '@i18n/routing'
import { useTranslations } from 'next-intl'

interface HeaderSectionProps {
  toolUrl: string
}

const HeaderSection: React.FC<HeaderSectionProps> = ({ toolUrl }) => {
  const t = useTranslations('imageExtenter')
  const [currentSlide, setCurrentSlide] = useState(0)

  // 轮播图数据 - 根据SEO文档的图片描述
  const carouselImages = [
    {
      id: 1,
      src: 'https://images.unsplash.com/photo-*************-21bda4d32df4?w=800&h=600&fit=crop',
      alt: 'A stunning landscape photo with a majestic mountain peak, AI expanding the sky and surrounding fields with glowing lines seamlessly blending together, showcasing the expansion process in a surreal style with vibrant colors',
      title: t('carouselTitle1'),
      subtitle: t('carouselSubtitle1'),
    },
    {
      id: 2,
      src: 'https://images.unsplash.com/photo-*************-2616c9c0e8e3?w=800&h=600&fit=crop',
      alt: 'A square portrait photo placed in the center of a larger canvas, AI technology intelligently filling the surrounding blank areas, creating a complete wider background from a simple indoor background to a complete living room scene',
      title: t('carouselTitle2'),
      subtitle: t('carouselSubtitle2'),
    },
    {
      id: 3,
      src: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=800&h=600&fit=crop',
      alt: 'AI image extender expanding a photo beyond its original borders with seamless generative fill technology',
      title: t('carouselTitle3'),
      subtitle: t('carouselSubtitle3'),
    },
  ]

  // 自动轮播
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % carouselImages.length)
    }, 5000)
    return () => clearInterval(timer)
  }, [carouselImages.length])

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % carouselImages.length)
  }

  const prevSlide = () => {
    setCurrentSlide(
      (prev) => (prev - 1 + carouselImages.length) % carouselImages.length
    )
  }

  return (
    <section className="min-h-screen relative bg-slate-900 overflow-hidden flex items-center">
      {/* 背景装饰 */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-32 h-32 bg-blue-500/10 rounded-full blur-2xl"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-blue-500/10 rounded-full blur-2xl"></div>
      </div>
      <video autoPlay muted playsInline loop className='w-full h-full absolute inset-0 object-cover opacity-50 max-md:object-contain' src="/images/image-extenter/hero.webm"></video>
      <div className="container mx-auto px-4 relative z-10">
        <div className="max-w-7xl mx-auto">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* 左侧内容 */}
            <div className="space-y-8">
              <div className="inline-flex items-center gap-2 bg-blue-500/10 rounded-full px-4 py-2 mb-6 border border-blue-500/20">
                <Sparkles className="w-4 h-4 text-blue-400" />
                <span className="text-blue-300 text-sm font-medium">
                  {t('aiPoweredBadge')}
                </span>
              </div>

              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white leading-tight">
                <span className="text-blue-400">{t('mainTitle')}</span>
                <br />
                <span className="text-white">{t('mainSubtitle')}</span>
              </h1>

              <p className="text-lg md:text-xl text-gray-300 leading-relaxed max-w-2xl">
                {t('mainDescription')}
              </p>

              {/* CTA按钮 */}
              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  href={toolUrl}
                  className="px-8 py-4 bg-blue-600 text-white font-semibold rounded-xl hover:bg-blue-700 transition-colors duration-200 flex items-center gap-2 justify-center"
                >
                  <Download className="w-5 h-5" />
                  {t('tryFreeButton')}
                </Link>

                {/* <button className="px-8 py-4 bg-transparent border-2 border-slate-600 text-gray-300 font-semibold rounded-xl hover:bg-slate-800 hover:border-slate-500 transition-colors duration-200">
                  {t('seeHowWorksButton')}
                </button> */}
              </div>

              {/* 小图标展示 */}
              <div className="flex items-center gap-4 pt-4 hidden">
                {[1, 2, 3, 4].map((i) => (
                  <div
                    key={i}
                    className="w-12 h-12 bg-slate-800 rounded-lg flex items-center justify-center border border-slate-700"
                  >
                    <Sparkles className="w-6 h-6 text-blue-400" />
                  </div>
                ))}
              </div>
            </div>

            {/* 右侧轮播图区域 */}
            <div className="relative hidden">
              {/* 主轮播图 */}
              <div className="relative w-full h-96 md:h-[500px] rounded-xl overflow-hidden shadow-xl bg-slate-800 border border-slate-700">
                {/* 轮播图片 */}
                <div className="relative w-full h-full">
                  {carouselImages.map((image, index) => (
                    <div
                      key={image.id}
                      className={`absolute inset-0 transition-opacity duration-700 ${
                        index === currentSlide ? 'opacity-100' : 'opacity-0'
                      }`}
                    >
                      <img
                        src={image.src}
                        alt={image.alt}
                        className="w-full h-full object-cover"
                      />
                      <div className="absolute inset-0 bg-black/30"></div>

                      {/* 图片标题 */}
                      <div className="absolute bottom-6 left-6 text-white">
                        <h3 className="text-lg font-semibold mb-1">
                          {image.title}
                        </h3>
                        <p className="text-sm text-gray-200">
                          {image.subtitle}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>

                {/* 导航按钮 */}
                <button
                  onClick={prevSlide}
                  className="absolute left-4 top-1/2 -translate-y-1/2 w-10 h-10 bg-black/60 text-white rounded-full flex items-center justify-center hover:bg-black/80 transition-colors duration-200"
                >
                  <ChevronLeft className="w-5 h-5" />
                </button>

                <button
                  onClick={nextSlide}
                  className="absolute right-4 top-1/2 -translate-y-1/2 w-10 h-10 bg-black/60 text-white rounded-full flex items-center justify-center hover:bg-black/80 transition-colors duration-200"
                >
                  <ChevronRight className="w-5 h-5" />
                </button>
              </div>

              {/* 缩略图预览 */}
              <div className="flex gap-2 mt-4">
                {carouselImages.map((image, index) => (
                  <button
                    key={image.id}
                    onClick={() => setCurrentSlide(index)}
                    className={`relative flex-1 aspect-video rounded-lg overflow-hidden border-2 transition-all duration-200 ${
                      index === currentSlide
                        ? 'border-blue-500'
                        : 'border-slate-600 hover:border-slate-500'
                    }`}
                  >
                    <img
                      src={image.src}
                      alt={`${t('previewAlt')} ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                    {index === currentSlide && (
                      <div className="absolute inset-0 bg-blue-500/20"></div>
                    )}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}

export default HeaderSection
